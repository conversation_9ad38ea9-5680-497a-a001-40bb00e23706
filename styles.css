/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    background: white;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    min-height: 100vh;
}

/* Header */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    text-align: center;
}

.header h1 {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 8px;
}

.header h2 {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 5px;
}

.header p {
    font-size: 14px;
    opacity: 0.9;
}

/* Product Info */
.product-info {
    padding: 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.product-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    max-width: 800px;
}

.product-row {
    display: flex;
    align-items: center;
}

.product-row .label {
    font-weight: 600;
    color: #495057;
    min-width: 120px;
    margin-right: 10px;
}

.product-row .value {
    color: #212529;
    font-weight: 500;
}

/* Controls */
.controls {
    padding: 20px;
    background: white;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.search-box {
    position: relative;
    flex: 1;
    max-width: 300px;
}

.search-box i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

.search-box input {
    width: 100%;
    padding: 10px 12px 10px 35px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.search-box input:focus {
    outline: none;
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

.filter-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.filter-controls select {
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 14px;
    background: white;
    cursor: pointer;
}

.btn-export {
    background: #28a745;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.15s ease-in-out;
}

.btn-export:hover {
    background: #218838;
}

/* Table */
.table-container {
    overflow-x: auto;
    margin: 0 20px;
}

.stock-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 13px;
    background: white;
}

.stock-table th {
    background: #f8f9fa;
    color: #495057;
    font-weight: 600;
    padding: 12px 8px;
    text-align: left;
    border: 1px solid #dee2e6;
    position: sticky;
    top: 0;
    z-index: 10;
}

.stock-table td {
    padding: 10px 8px;
    border: 1px solid #dee2e6;
    vertical-align: middle;
}

.stock-table tbody tr:nth-child(even) {
    background: #f8f9fa;
}

.stock-table tbody tr:hover {
    background: #e3f2fd;
}

/* Status badges */
.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
}

.status-done {
    background: #d4edda;
    color: #155724;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-cancelled {
    background: #f8d7da;
    color: #721c24;
}

/* Quantity columns */
.qty-in {
    color: #28a745;
    font-weight: 600;
}

.qty-out {
    color: #dc3545;
    font-weight: 600;
}

.qty-balance {
    color: #007bff;
    font-weight: 600;
}

/* Summary */
.summary {
    padding: 20px;
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    gap: 20px;
}

.summary-item {
    text-align: center;
}

.summary-item .label {
    display: block;
    font-size: 14px;
    color: #6c757d;
    margin-bottom: 5px;
}

.summary-item .value {
    display: block;
    font-size: 20px;
    font-weight: bold;
    color: #495057;
}

/* Footer */
.footer {
    padding: 20px;
    text-align: center;
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
    color: #6c757d;
    font-size: 12px;
}

.footer p {
    margin-bottom: 5px;
}

/* Responsive */
@media (max-width: 768px) {
    .controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-box {
        max-width: none;
    }
    
    .filter-controls {
        justify-content: space-between;
    }
    
    .product-details {
        grid-template-columns: 1fr;
    }
    
    .summary {
        flex-direction: column;
        text-align: center;
    }
    
    .stock-table {
        font-size: 12px;
    }
    
    .stock-table th,
    .stock-table td {
        padding: 8px 4px;
    }
}

/* Loading animation */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px;
}

.loading::after {
    content: '';
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
