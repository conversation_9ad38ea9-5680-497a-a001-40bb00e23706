<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sistem Kartu Stok - PT. Keluarga Sukses Mulia</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="company-info">
                <h1>PT. KELUARGA SUKSES MULIA</h1>
                <h2>Kartu Stok Persediaan</h2>
                <p>Dari 01 Jan 2025 s/d 06 Agu 2025</p>
            </div>
        </div>

        <!-- Product Info -->
        <div class="product-info">
            <div class="product-details">
                <div class="product-row">
                    <span class="label">Barang:</span>
                    <span class="value">GENERIK PARACETAMOL DROPS IFARS 15ML (B)</span>
                </div>
                <div class="product-row">
                    <span class="label">Kode Barang:</span>
                    <span class="value">2210120002</span>
                </div>
                <div class="product-row">
                    <span class="label">Nama Barang:</span>
                    <span class="value">GENERIK PARACETAMOL DROPS IFARS 15ML (B)</span>
                </div>
            </div>
        </div>

        <!-- Controls -->
        <div class="controls">
            <div class="search-box">
                <i class="fas fa-search"></i>
                <input type="text" id="searchInput" placeholder="Cari transaksi...">
            </div>
            <div class="filter-controls">
                <select id="typeFilter">
                    <option value="">Semua Tipe</option>
                    <option value="Saldo Awal">Saldo Awal</option>
                    <option value="Penyesuaian Persediaan">Penyesuaian Persediaan</option>
                    <option value="Faktur Pembelian">Faktur Pembelian</option>
                    <option value="Faktur Penjualan">Faktur Penjualan</option>
                    <option value="Pengiriman Pesanan">Pengiriman Pesanan</option>
                    <option value="Retur Penjualan">Retur Penjualan</option>
                </select>
                <button id="exportBtn" class="btn-export">
                    <i class="fas fa-download"></i>
                    Export
                </button>
            </div>
        </div>

        <!-- Stock Movement Table -->
        <div class="table-container">
            <table class="stock-table" id="stockTable">
                <thead>
                    <tr>
                        <th>Tanggal</th>
                        <th>Tipe Transaksi</th>
                        <th>Nomor #</th>
                        <th>Deskripsi</th>
                        <th>QTY Masuk</th>
                        <th>QTY Keluar</th>
                        <th>QTY Akhir</th>
                        <th>No. Batch</th>
                        <th>Expired</th>
                        <th>Gudang</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody id="stockTableBody">
                    <!-- Data will be populated by JavaScript -->
                </tbody>
            </table>
        </div>

        <!-- Summary -->
        <div class="summary">
            <div class="summary-item">
                <span class="label">Total Masuk:</span>
                <span class="value" id="totalMasuk">0</span>
            </div>
            <div class="summary-item">
                <span class="label">Total Keluar:</span>
                <span class="value" id="totalKeluar">0</span>
            </div>
            <div class="summary-item">
                <span class="label">Stok Akhir:</span>
                <span class="value" id="stokAkhir">0</span>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>ACCURATE Accounting System Report</p>
            <p>Tercetak pada <span id="currentDate"></span></p>
            <p>Halaman 1 dari 1</p>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
