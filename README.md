# Sistem Kartu Stok - PT. Keluarga Sukses Mulia

Sistem sederhana untuk menampilkan kartu stok persediaan dengan tampilan mirip Odoo, tanpa database, menggunakan data dummy.

## Fitur

- **<PERSON><PERSON><PERSON> Mirip Odoo**: Interface yang clean dan profesional seperti sistem ERP Odoo
- **Data Dummy**: Data stock movement berdasarkan spreadsheet yang diberikan
- **Pencarian**: Fitur pencarian berdasarkan deskripsi, nomor transaksi, tipe, atau gudang
- **Filter**: Filter berdasarkan tipe transaksi
- **Export CSV**: Ekspor data ke file CSV
- **Responsive**: <PERSON><PERSON><PERSON> yang responsif untuk berbagai ukuran layar
- **Summary**: Ringkasan total masuk, keluar, dan stok akhir

## Struktur File

```
apotek_contoh/
├── index.html          # File HTML utama
├── styles.css          # Styling CSS
├── script.js           # JavaScript untuk fungsionalitas
└── README.md           # Dokumentasi
```

## Cara Menjalankan

1. Buka file `index.html` di browser web
2. Sistem akan menampilkan data stock movement untuk produk "GENERIK PARACETAMOL DROPS IFARS 15ML (B)"
3. Gunakan fitur pencarian dan filter untuk menyaring data
4. Klik tombol "Export" untuk mengunduh data dalam format CSV

## Data yang Ditampilkan

Sistem menampilkan data stock movement dengan kolom:
- **Tanggal**: Tanggal transaksi
- **Tipe Transaksi**: Jenis transaksi (Saldo Awal, Pembelian, Penjualan, dll.)
- **Nomor #**: Nomor dokumen transaksi
- **Deskripsi**: Deskripsi detail transaksi
- **QTY Masuk**: Jumlah barang masuk
- **QTY Keluar**: Jumlah barang keluar
- **QTY Akhir**: Saldo akhir setelah transaksi
- **No. Batch**: Nomor batch produk
- **Expired**: Tanggal kadaluarsa
- **Gudang**: Lokasi gudang
- **Status**: Status transaksi (Done/Pending/Cancelled)

## Tipe Transaksi

- **Saldo Awal**: Saldo pembukaan
- **Penyesuaian Persediaan**: Adjustment inventory
- **Faktur Pembelian**: Purchase invoice
- **Faktur Penjualan**: Sales invoice
- **Pengiriman Pesanan**: Delivery order
- **Retur Penjualan**: Sales return

## Teknologi yang Digunakan

- **HTML5**: Struktur halaman
- **CSS3**: Styling dan layout responsif
- **JavaScript (Vanilla)**: Fungsionalitas interaktif
- **Font Awesome**: Icons
- **No Database**: Menggunakan data dummy dalam JavaScript

## Kustomisasi

Untuk mengubah data, edit array `stockMovements` di file `script.js`. Setiap item memiliki struktur:

```javascript
{
    date: 'DD MMM YYYY',
    type: 'Tipe Transaksi',
    number: 'Nomor Dokumen',
    description: 'Deskripsi',
    qtyIn: 0,           // Jumlah masuk
    qtyOut: 0,          // Jumlah keluar
    qtyBalance: 0,      // Saldo akhir
    batch: 'Batch',
    expired: 'DD MMM YYYY',
    warehouse: 'Nama Gudang',
    status: 'Done'      // Done/Pending/Cancelled
}
```

## Browser Support

- Chrome (recommended)
- Firefox
- Safari
- Edge

## Catatan

- Sistem ini menggunakan data dummy dan tidak terhubung ke database
- Data akan reset setiap kali halaman di-refresh
- Untuk implementasi production, perlu integrasi dengan backend dan database
