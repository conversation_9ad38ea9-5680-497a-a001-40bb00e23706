// Dummy stock movement data based on the spreadsheet
const stockMovements = [
    {
        date: '31 Dec 2024',
        type: '<PERSON><PERSON>',
        number: '',
        description: '<PERSON>do <PERSON> 31 Dec 2024',
        qtyIn: 0,
        qtyOut: 0,
        qtyBalance: 0,
        batch: '',
        expired: '',
        warehouse: 'GUDANG UTAMA',
        status: 'Done'
    },
    {
        date: '08/07/2025',
        type: 'Penyesuaian Persediaan',
        number: 'IA.2025.01.00237',
        description: 'Penyesuaian IA.2025.01.00237',
        qtyIn: 3,
        qtyOut: 0,
        qtyBalance: 3,
        batch: '40428',
        expired: '08/07/2025',
        warehouse: 'GUDANG UTAMA',
        status: 'Done'
    },
    {
        date: '08/07/2025',
        type: 'Faktur Pembelian',
        number: 'PI.2025.03.00316',
        description: 'Pembelian dari GREAT DELI FARMA PT',
        qtyIn: 6,
        qtyOut: 0,
        qtyBalance: 9,
        batch: '40428',
        expired: '08/07/2025',
        warehouse: 'GUDANG UTAMA',
        status: 'Done'
    },
    {
        date: '08/07/2025',
        type: 'Faktur Penjualan',
        number: 'SI.2025.04.00575',
        description: 'Penjualan ke AK22 - APOTEK KELUARGA 22',
        qtyIn: 0,
        qtyOut: 1,
        qtyBalance: 8,
        batch: '40428',
        expired: '08/07/2025',
        warehouse: 'GUDANG UTAMA',
        status: 'Done'
    },
    {
        date: '08/07/2025',
        type: 'Faktur Penjualan',
        number: 'SI.2025.04.00580',
        description: 'Penjualan ke AK17 - APOTEK KELUARGA 17',
        qtyIn: 0,
        qtyOut: 2,
        qtyBalance: 6,
        batch: '40428',
        expired: '08/07/2025',
        warehouse: 'GUDANG UTAMA',
        status: 'Done'
    },
    {
        date: '08/07/2025',
        type: 'Faktur Penjualan',
        number: 'SI.2025.04.01563',
        description: 'Penjualan ke AK02 - APOTEK KELUARGA 2',
        qtyIn: 0,
        qtyOut: 1,
        qtyBalance: 5,
        batch: '40428',
        expired: '08/07/2025',
        warehouse: 'GUDANG UTAMA',
        status: 'Done'
    },
    {
        date: '08/07/2025',
        type: 'Faktur Penjualan',
        number: 'SI.2025.04.02313',
        description: 'Penjualan ke AK01 - APOTEK KELUARGA',
        qtyIn: 0,
        qtyOut: 1,
        qtyBalance: 4,
        batch: '40228',
        expired: '08/07/2025',
        warehouse: 'GUDANG UTAMA',
        status: 'Done'
    },
    {
        date: '03 May 2025',
        type: 'Faktur Pembelian',
        number: 'PI.2025.04.00945',
        description: 'Pembelian dari GREAT DELI FARMA PT',
        qtyIn: 5,
        qtyOut: 0,
        qtyBalance: 9,
        batch: '40228',
        expired: '03 May 2025',
        warehouse: 'GUDANG UTAMA',
        status: 'Done'
    },
    {
        date: '03 May 2025',
        type: 'Faktur Penjualan',
        number: 'SI.2025.05.00069',
        description: 'Penjualan ke AK12 - APOTEK KELUARGA 12',
        qtyIn: 0,
        qtyOut: 1,
        qtyBalance: 8,
        batch: '40228',
        expired: '03 May 2025',
        warehouse: 'GUDANG UTAMA',
        status: 'Done'
    },
    {
        date: '05 May 2025',
        type: 'Faktur Penjualan',
        number: 'SI.2025.05.00097',
        description: 'Penjualan ke AK24 - APOTEK KELUARGA 24',
        qtyIn: 0,
        qtyOut: 2,
        qtyBalance: 6,
        batch: '40228',
        expired: '05 May 2025',
        warehouse: 'GUDANG UTAMA',
        status: 'Done'
    },
    {
        date: '05 May 2025',
        type: 'Faktur Penjualan',
        number: 'SI.2025.05.00114',
        description: 'Penjualan ke AK23 - APOTEK KELUARGA 23',
        qtyIn: 0,
        qtyOut: 1,
        qtyBalance: 5,
        batch: '40228',
        expired: '05 May 2025',
        warehouse: 'GUDANG UTAMA',
        status: 'Done'
    },
    {
        date: '05 May 2025',
        type: 'Faktur Penjualan',
        number: 'SI.2025.05.00126',
        description: 'Penjualan ke AK04 - APOTEK KELUARGA 4',
        qtyIn: 0,
        qtyOut: 1,
        qtyBalance: 4,
        batch: '40228',
        expired: '05 May 2025',
        warehouse: 'GUDANG UTAMA',
        status: 'Done'
    },
    {
        date: '13 May 2025',
        type: 'Faktur Pembelian',
        number: 'PI.2025.05.00452',
        description: 'Pembelian dari GREAT DELI FARMA PT',
        qtyIn: 8,
        qtyOut: 0,
        qtyBalance: 12,
        batch: '40228',
        expired: '13 May 2025',
        warehouse: 'GUDANG UTAMA',
        status: 'Done'
    },
    {
        date: '24 May 2025',
        type: 'Faktur Penjualan',
        number: 'SI.2025.05.02541',
        description: 'Penjualan ke AK12 - APOTEK KELUARGA 12',
        qtyIn: 0,
        qtyOut: 1,
        qtyBalance: 11,
        batch: '40228',
        expired: '24 May 2025',
        warehouse: 'GUDANG UTAMA',
        status: 'Done'
    },
    {
        date: '31 May 2025',
        type: 'Faktur Penjualan',
        number: 'SI.2025.05.03261',
        description: 'Penjualan ke AK12 - APOTEK KELUARGA 12',
        qtyIn: 0,
        qtyOut: 2,
        qtyBalance: 9,
        batch: '40228',
        expired: '31 May 2025',
        warehouse: 'GUDANG UTAMA',
        status: 'Done'
    },
    {
        date: '06 Jun 2025',
        type: 'Pengiriman Pesanan',
        number: 'DO.2025.06.00308',
        description: 'Pengiriman ke AK12 - APOTEK KELUARGA 12',
        qtyIn: 0,
        qtyOut: 1,
        qtyBalance: 8,
        batch: '46241',
        expired: '06 Jun 2025',
        warehouse: 'GUDANG UTAMA',
        status: 'Done'
    },
    {
        date: '06 Jun 2025',
        type: 'Pengiriman Pesanan',
        number: 'DO.2025.06.03716',
        description: 'Pengiriman ke AK22 - APOTEK KELUARGA 22',
        qtyIn: 0,
        qtyOut: 1,
        qtyBalance: 7,
        batch: '46241',
        expired: '06 Jun 2025',
        warehouse: 'GUDANG UTAMA',
        status: 'Done'
    },
    {
        date: '06 Jun 2025',
        type: 'Faktur Pembelian',
        number: 'PI.2025.06.01161',
        description: 'Pembelian dari GREAT DELI FARMA PT',
        qtyIn: 7,
        qtyOut: 0,
        qtyBalance: 14,
        batch: '46241',
        expired: '06 Jun 2025',
        warehouse: 'GUDANG UTAMA',
        status: 'Done'
    },
    {
        date: '06 Jun 2025',
        type: 'Pengiriman Pesanan',
        number: 'DO.2025.06.04243',
        description: 'Pengiriman ke AK22 - APOTEK KELUARGA 22',
        qtyIn: 0,
        qtyOut: 1,
        qtyBalance: 13,
        batch: '46241',
        expired: '06 Jun 2025',
        warehouse: 'GUDANG UTAMA',
        status: 'Done'
    },
    {
        date: '07 Jul 2025',
        type: 'Pengiriman Pesanan',
        number: 'DO.2025.07.01095',
        description: 'Pengiriman ke AK22 - APOTEK KELUARGA 22',
        qtyIn: 0,
        qtyOut: 3,
        qtyBalance: 10,
        batch: '46241',
        expired: '07 Jul 2025',
        warehouse: 'GUDANG UTAMA',
        status: 'Done'
    },
    {
        date: '07 Jul 2025',
        type: 'Retur Penjualan',
        number: 'SRT.2025.07.00041',
        description: 'Retur dari AK22 - APOTEK KELUARGA 22',
        qtyIn: 1,
        qtyOut: 0,
        qtyBalance: 11,
        batch: '46241',
        expired: '07 Jul 2025',
        warehouse: 'GUDANG UTAMA',
        status: 'Done'
    },
    {
        date: '07 Jul 2025',
        type: 'Faktur Pembelian',
        number: 'PI.2025.07.00881',
        description: 'Pembelian dari GREAT DELI FARMA PT',
        qtyIn: 1,
        qtyOut: 0,
        qtyBalance: 12,
        batch: '46241',
        expired: '07 Jul 2025',
        warehouse: 'GUDANG UTAMA',
        status: 'Done'
    },
    {
        date: '07 Jul 2025',
        type: 'Pengiriman Pesanan',
        number: 'DO.2025.07.04105',
        description: 'Pengiriman ke AK12 - APOTEK KELUARGA 12',
        qtyIn: 0,
        qtyOut: 1,
        qtyBalance: 11,
        batch: '46241',
        expired: '07 Jul 2025',
        warehouse: 'GUDANG UTAMA',
        status: 'Done'
    },
    {
        date: '06 Aug 2025',
        type: 'Pengiriman Pesanan',
        number: 'DO.2025.08.01238',
        description: 'Pengiriman ke AK06 - APOTEK KELUARGA 6',
        qtyIn: 0,
        qtyOut: 1,
        qtyBalance: 10,
        batch: '46241',
        expired: '06 Aug 2025',
        warehouse: 'GUDANG UTAMA',
        status: 'Done'
    }
];

// Global variables
let filteredData = [...stockMovements];

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    renderTable();
    updateSummary();
    setCurrentDate();
    setupEventListeners();
}

function setupEventListeners() {
    // Search functionality
    document.getElementById('searchInput').addEventListener('input', handleSearch);
    
    // Filter functionality
    document.getElementById('typeFilter').addEventListener('change', handleFilter);
    
    // Export functionality
    document.getElementById('exportBtn').addEventListener('click', handleExport);
}

function handleSearch(event) {
    const searchTerm = event.target.value.toLowerCase();
    
    filteredData = stockMovements.filter(item => 
        item.description.toLowerCase().includes(searchTerm) ||
        item.number.toLowerCase().includes(searchTerm) ||
        item.type.toLowerCase().includes(searchTerm) ||
        item.warehouse.toLowerCase().includes(searchTerm)
    );
    
    renderTable();
    updateSummary();
}

function handleFilter(event) {
    const filterType = event.target.value;
    
    if (filterType === '') {
        filteredData = [...stockMovements];
    } else {
        filteredData = stockMovements.filter(item => item.type === filterType);
    }
    
    renderTable();
    updateSummary();
}

function renderTable() {
    const tbody = document.getElementById('stockTableBody');
    tbody.innerHTML = '';
    
    filteredData.forEach(item => {
        const row = createTableRow(item);
        tbody.appendChild(row);
    });
}

function createTableRow(item) {
    const row = document.createElement('tr');
    
    row.innerHTML = `
        <td>${item.date}</td>
        <td>${item.type}</td>
        <td>${item.number}</td>
        <td>${item.description}</td>
        <td class="qty-in">${item.qtyIn > 0 ? item.qtyIn : ''}</td>
        <td class="qty-out">${item.qtyOut > 0 ? item.qtyOut : ''}</td>
        <td class="qty-balance">${item.qtyBalance}</td>
        <td>${item.batch}</td>
        <td>${item.expired}</td>
        <td>${item.warehouse}</td>
        <td><span class="status-badge status-${item.status.toLowerCase()}">${item.status}</span></td>
    `;
    
    return row;
}

function updateSummary() {
    const totalMasuk = filteredData.reduce((sum, item) => sum + item.qtyIn, 0);
    const totalKeluar = filteredData.reduce((sum, item) => sum + item.qtyOut, 0);
    const stokAkhir = filteredData.length > 0 ? filteredData[filteredData.length - 1].qtyBalance : 0;
    
    document.getElementById('totalMasuk').textContent = totalMasuk;
    document.getElementById('totalKeluar').textContent = totalKeluar;
    document.getElementById('stokAkhir').textContent = stokAkhir;
}

function setCurrentDate() {
    const now = new Date();
    const options = { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    };
    const dateString = now.toLocaleDateString('id-ID', options);
    document.getElementById('currentDate').textContent = dateString;
}

function handleExport() {
    // Simple CSV export functionality
    const headers = ['Tanggal', 'Tipe Transaksi', 'Nomor #', 'Deskripsi', 'QTY Masuk', 'QTY Keluar', 'QTY Akhir', 'No. Batch', 'Expired', 'Gudang', 'Status'];
    
    let csvContent = headers.join(',') + '\n';
    
    filteredData.forEach(item => {
        const row = [
            item.date,
            item.type,
            item.number,
            `"${item.description}"`,
            item.qtyIn,
            item.qtyOut,
            item.qtyBalance,
            item.batch,
            item.expired,
            item.warehouse,
            item.status
        ];
        csvContent += row.join(',') + '\n';
    });
    
    // Create and download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'kartu_stok_paracetamol.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}
